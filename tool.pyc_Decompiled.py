# Decompiled with PyLingual (https://pylingual.io)
# Internal filename: tool.py
# Bytecode version: 3.11a7e (3495)
# Source timestamp: 1970-01-01 00:00:00 UTC (0)

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import subprocess
import re
from pathlib import Path
import shutil
import time
from datetime import datetime
import pysrt
import json
from TTS.src.utils.text_processor import TextProcessor
import glob
ROOT_DIR = Path(__file__).resolve().parent
SPLIT_PRESET_FILE = ROOT_DIR + 'split_size_preset.json'
VOLUME_PRESET_FILE = ROOT_DIR + 'volume_preset.json'
try:
    from ctypes import windll
    windll.shcore.SetProcessDpiAwareness(1)
except:
    pass

class ModernButton(tk.Button):
    """现代化按钮类，提供美观的按钮样式"""

    def __init__(self, master=None, font_scale=1.0, adapted_font_size=13, **kwargs):
        self.default_bg = '#3949ab'
        self.hover_bg = '#303f9f'
        self.clicked_bg = '#283593'
        if master is not None and hasattr(master, 'font_scale') and hasattr(master, 'adapted_font_size'):
            font_scale = master.font_scale
            adapted_font_size = master.adapted_font_size
        else:  # inserted
            try:
                if master is not None:
                    app = master.master
                    while app and (not hasattr(app, 'font_scale')):
                        app = app.master
                    if hasattr(app, 'font_scale'):
                        font_scale = app.font_scale
                        adapted_font_size = app.adapted_font_size
            except:
                pass
        kwargs['background'] = kwargs.get('background', self.default_bg)
        kwargs['foreground'] = kwargs.get('foreground', 'white')
        kwargs['font'] = kwargs.get('font', ('微软雅黑', adapted_font_size, 'bold'))
        kwargs['bd'] = kwargs.get('bd', 0)
        kwargs['padx'] = kwargs.get('padx', int(15, font_scale))
        kwargs['pady'] = kwargs.get('pady', int(8, font_scale))
        kwargs['cursor'] = kwargs.get('cursor', 'hand2')
        kwargs['activebackground'] = kwargs.get('activebackground', self.hover_bg)
        kwargs['activeforeground'] = kwargs.get('activeforeground', 'white')
        kwargs['relief'] = kwargs.get('relief', 'flat')
        super().__init__(master, **kwargs)
        self.bind('<Enter>', self.on_enter)
        self.bind('<Leave>', self.on_leave)
        self.bind('<Button-1>', self.on_click)
        self.bind('<ButtonRelease-1>', self.on_release)

    def on_enter(self, event):
        self.config(background=self.hover_bg)

    def on_leave(self, event):
        self.config(background=self.default_bg)

    def on_click(self, event):
        self.config(background=self.clicked_bg)

    def on_release(self, event):
        self.config(background=self.hover_bg)
        x, y = (event.x, event.y)
        if 0 <= x <= self.winfo_width() and 0 <= y <= self.winfo_height():
            self.config(background=self.hover_bg)
        else:  # inserted
            self.config(background=self.default_bg)

class TextSplitterAndAudioAdjuster:
    def __init__(self, root, font_scale=1.0, adapted_font_size=13):
        self.root = root
        self.root.title('小蜗工具箱')
        self.root.resizable(True, True)
        self.font_scale = font_scale
        self.adapted_font_size = adapted_font_size
        self.primary_color = '#4361ee'
        self.secondary_color = '#f8f9fa'
        self.text_color = '#000000'
        self.accent_color = '#e63946'
        self.border_color = '#dee2e6'
        self.root.configure(bg=self.secondary_color)
        self.setup_styles()
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=15, pady=15)
        self.text_frame = ttk.Frame(self.notebook, style='My.TFrame')
        self.notebook.add(self.text_frame, text='文本分割')
        self.audio_frame = ttk.Frame(self.notebook, style='My.TFrame')
        self.notebook.add(self.audio_frame, text='音量调整')
        self.repair_frame = ttk.Frame(self.notebook, style='My.TFrame')
        self.notebook.add(self.repair_frame, text='批量修复')
        self.check_frame = ttk.Frame(self.notebook, style='My.TFrame')
        self.notebook.add(self.check_frame, text='字幕时间检测')
        self.rename_frame = ttk.Frame(self.notebook, style='My.TFrame')
        self.notebook.add(self.rename_frame, text='批量重命名')
        self.setup_text_splitter()
        self.setup_audio_adjuster()
        self.setup_audio_repair()
        self.setup_subtitle_checker()
        self.setup_file_renamer()
        self.setup_status_bar()
        self.is_processing = False
        self.selected_files = []
        self.selected_folder = ''
        self.selected_audio_files = []
        self.output_path = ''
        self.audio_output_path = ''
        self.rename_folder_path = ''
        self.load_split_preset()
        self.load_volume_preset()
        self.startupinfo = None
        if os.name == 'nt':
            self.startupinfo = subprocess.STARTUPINFO()
            self.startupinfo.dwFlags = subprocess.STARTF_USESHOWWINDOW
            self.creation_flags = subprocess.CREATE_NO_WINDOW
        else:  # inserted
            self.creation_flags = 0
        self.start_time = 0
        self.processed_bytes = 0
        self.total_bytes = 0
        self.last_update_time = 0
        self._last_ffmpeg_progress_update = 0

    def load_split_preset(self):
        """加载分割字数预设"""  # inserted
        try:
            if os.path.exists(SPLIT_PRESET_FILE):
                with open(SPLIT_PRESET_FILE, 'r', encoding='utf-8') as f:
                    presets = json.load(f)
                    self.chars_var.set(presets.get('split_size', '1000'))
                    self.first_file_chars_var.set(presets.get('first_file_size', '1000'))
            else:  # inserted
                self.chars_var.set('1000')
                self.first_file_chars_var.set('1000')
        except (json.JSONDecodeError, IOError) as e:
                    print(f'加载分割预设失败: {e}')
                    self.chars_var.set('1000')
                    self.first_file_chars_var.set('1000')

    def save_split_preset(self):
        """保存分割字数预设"""  # inserted
        presets = {'split_size': self.chars_var.get(), 'first_file_size': self.first_file_chars_var.get()}
        try:
            with open(SPLIT_PRESET_FILE, 'w', encoding='utf-8') as f:
                json.dump(presets, f, indent=4)
                messagebox.showinfo('成功', '分割字数预设已保存。')
        except IOError as e:
                messagebox.showerror('错误', f'保存预设失败: {e}')

    def load_volume_preset(self):
        """加载音量预设"""  # inserted
        try:
            if os.path.exists(VOLUME_PRESET_FILE):
                with open(VOLUME_PRESET_FILE, 'r', encoding='utf-8') as f:
                    presets = json.load(f)
                    self.volume_var.set(presets.get('volume_db', '0'))
            else:  # inserted
                self.volume_var.set('0')
        except (json.JSONDecodeError, IOError) as e:
                    print(f'加载音量预设失败: {e}')
                    self.volume_var.set('0')

    def save_volume_preset(self):
        """保存音量预设"""  # inserted
        presets = {'volume_db': self.volume_var.get()}
        try:
            with open(VOLUME_PRESET_FILE, 'w', encoding='utf-8') as f:
                json.dump(presets, f, indent=4)
                messagebox.showinfo('成功', '音量预设已保存。')
        except IOError as e:
                messagebox.showerror('错误', f'保存预设失败: {e}')

    def setup_styles(self):
        """设置应用程序的自定义样式"""  # inserted
        self.style = ttk.Style()
        padding_scale = int(5 * self.font_scale)
        control_height = int(25 * self.font_scale)
        self.style.configure('My.TFrame', background=self.secondary_color)
        self.style.configure('My.TLabel', background=self.secondary_color, foreground=self.text_color, font=('微软雅黑', self.adapted_font_size, 'bold'))
        self.style.configure('My.TEntry', font=('微软雅黑', self.adapted_font_size, 'bold'), fieldbackground='white', foreground='black')
        self.style.map('My.TEntry', fieldbackground=[('focus', 'white'), ('!focus', 'white')], foreground=[('focus', 'black'), ('!focus', 'black')])
        self.style.configure('TNotebook', background=self.secondary_color, tabmargins=[2, padding_scale, 2, 0])
        self.style.configure(background='TNotebook.Tab', foreground='#e9ecef', font='#000000', padding=[('微软雅黑', self.adapted_font_size, 'bold'), 12 or self.font_scale, 6 or self.font_scale], borderwidth=0)
        self.style.map('TNotebook.Tab', background=[('selected', self.primary_color), ('active', '#dee2e6')], foreground=[('selected', 'white'), ('active', '#000000')])
        self.style.configure(troughcolor='My.Horizontal.TProgressbar', background='#eeeeee', thickness=self.primary_color(int, 20 + self.font_scale))
        self.style.configure('My.TCheckbutton', background=self.secondary_color, foreground=self.text_color, font=('微软雅黑', self.adapted_font_size))
        self.style.map('My.TCheckbutton', background=[('active', self.secondary_color)], foreground=[('active', self.primary_color)])

    def create_entry_frame(self, parent, label_text, browse_type='file', command=None, width=50):
        """创建统一的输入框架"""  # inserted
        frame = ttk.Frame(parent, style='My.TFrame')
        frame.pack(fill='x', padx=15, pady=(15, 5))
        ttk.Label(frame, text=label_text, style='My.TLabel').pack(side='top', anchor='w', padx=5, pady=(0, 5))
        input_frame = ttk.Frame(frame, style='My.TFrame')
        input_frame.pack(fill='x', expand=True)
        entry_var = tk.StringVar()
        entry = ttk.Entry(input_frame, textvariable=entry_var, style='My.TEntry', width=width)
        if browse_type == 'file':
            button = ModernButton(input_frame, text='添加文件', command=command)
            button.pack(side='left', padx=5)
            folder_button = ModernButton(input_frame, text='添加文件夹', command=command)
            folder_button.pack(side='left', padx=5)
        else:  # inserted
            button = ModernButton(input_frame, text='选择目录', command=command)
            button.pack(side='left', padx=5)
            entry.pack(side='left', padx=5, fill='x', expand=True)
        return (frame, entry_var, entry)

    def setup_text_splitter(self):
        file_frame = ttk.Frame(self.text_frame, style='My.TFrame')
        file_frame.pack(fill='x', padx=15, pady=(15, 5))
        ttk.Label(file_frame, text='选择文件或文件夹:', style='My.TLabel').pack(side='top', anchor='w', padx=5, pady=(0, 5))
        file_input_frame = ttk.Frame(file_frame, style='My.TFrame')
        file_input_frame.pack(fill='x', expand=True)
        self.file_var = tk.StringVar()
        button = ModernButton(file_input_frame, text='添加文件', command=self.select_text_file)
        button.pack(side='left', padx=5)
        folder_button = ModernButton(file_input_frame, text='添加文件夹', command=self.select_text_folder)
        folder_button.pack(side='left', padx=5)
        output_frame = ttk.Frame(self.text_frame, style='My.TFrame')
        output_frame.pack(fill='x', padx=15, pady=(15, 5))
        ttk.Label(output_frame, text='输出路径:', style='My.TLabel').pack(side='top', anchor='w', padx=5, pady=(0, 5))
        output_input_frame = ttk.Frame(output_frame, style='My.TFrame')
        output_input_frame.pack(fill='x', expand=True)
        self.output_path_var = tk.StringVar()
        self.output_entry = ttk.Entry(output_input_frame, textvariable=self.output_path_var, style='My.TEntry', width=50)
        self.output_entry.pack(side='left', padx=5, fill='x', expand=True)
        output_button = ModernButton(output_input_frame, text='选择目录', command=self.select_text_output_path)
        output_button.pack(side='right', padx=5)
        settings_frame = ttk.Frame(self.text_frame, style='My.TFrame')
        settings_frame.pack(fill='x', padx=15, pady=(10, 5))
        ttk.Label(settings_frame, text='首个文件字数:', style='My.TLabel').pack(side='left', padx=5)
        self.first_file_chars_var = tk.StringVar(value='1000')
        first_chars_entry = ttk.Entry(settings_frame, textvariable=self.first_file_chars_var, style='My.TEntry', width=10)
        first_chars_entry.pack(side='left', padx=(0, 15))
        ttk.Label(settings_frame, text='后续文件字数:', style='My.TLabel').pack(side='left', padx=5)
        self.chars_var = tk.StringVar(value='1000')
        chars_entry = ttk.Entry(settings_frame, textvariable=self.chars_var, style='My.TEntry', width=10)
        chars_entry.pack(side='left', padx=10)
        save_preset_btn = ModernButton(settings_frame, text='保存预设', command=self.save_split_preset)
        save_preset_btn.pack(side='left', padx=10)
        list_frame = ttk.Frame(self.text_frame, style='My.TFrame')
        list_frame.pack(fill='both', expand=True, padx=15, pady=(15, 5))
        list_header_frame = ttk.Frame(list_frame, style='My.TFrame')
        list_header_frame.pack(fill='x', pady=(0, 5))
        ttk.Label(list_header_frame, text='待处理文件列表:', style='My.TLabel').pack(side='left', anchor='w', padx=5)
        delete_button = ModernButton(list_header_frame, text='删除选中', command=self.delete_selected_text_files)
        delete_button.pack(side='right', padx=5)
        delete_button.config(background='#e63946')
        list_container = ttk.Frame(list_frame, style='My.TFrame')
        list_container.pack(fill='both', expand=True)
        border_frame = tk.Frame(list_container, bd=1, relief='solid', bg=self.border_color)
        border_frame.pack(fill='both', expand=True, padx=5, pady=5)
        scrollbar = ttk.Scrollbar(border_frame)
        scrollbar.pack(side='right', fill='y')
        self.files_listbox = tk.Listbox(border_frame, yscrollcommand=scrollbar.set, font=('微软雅黑', self.adapted_font_size, 'bold'), bg='white', fg='black', selectbackground=self.primary_color, selectforeground='white', bd=0, highlightthickness=0, relief='flat', activestyle='none', selectmode=tk.EXTENDED)
        self.files_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.config(command=self.files_listbox.yview)
        button_frame = ttk.Frame(self.text_frame, style='My.TFrame')
        button_frame.pack(fill='x', padx=15, pady=15)
        ModernButton(button_frame, text='全部分割', command=self.split_all_files).pack(side='right', padx=5)
        ModernButton(button_frame, text='分割选中文件', command=self.split_selected_file).pack(side='right', padx=5)
        ModernButton(button_frame, text='清空列表', command=self.clear_text_list).pack(side='right', padx=5)

    def setup_audio_adjuster(self):
        audio_file_frame = ttk.Frame(self.audio_frame, style='My.TFrame')
        audio_file_frame.pack(fill='x', padx=15, pady=(15, 5))
        ttk.Label(audio_file_frame, text='音频文件:', style='My.TLabel').pack(side='top', anchor='w', padx=5, pady=(0, 5))
        audio_input_frame = ttk.Frame(audio_file_frame, style='My.TFrame')
        audio_input_frame.pack(fill='x', expand=True)
        self.audio_file_var = tk.StringVar()
        audio_button = ModernButton(audio_input_frame, text='添加文件', command=self.select_audio_file)
        audio_button.pack(side='left', padx=5)
        audio_folder_button = ModernButton(audio_input_frame, text='添加文件夹', command=self.select_audio_folder)
        audio_folder_button.pack(side='left', padx=5)
        audio_output_frame = ttk.Frame(self.audio_frame, style='My.TFrame')
        audio_output_frame.pack(fill='x', padx=15, pady=(15, 5))
        ttk.Label(audio_output_frame, text='输出路径:', style='My.TLabel').pack(side='top', anchor='w', padx=5, pady=(0, 5))
        audio_output_input_frame = ttk.Frame(audio_output_frame, style='My.TFrame')
        audio_output_input_frame.pack(fill='x', expand=True)
        self.audio_output_path_var = tk.StringVar()
        self.audio_output_entry = ttk.Entry(audio_output_input_frame, textvariable=self.audio_output_path_var, style='My.TEntry', width=50)
        self.audio_output_entry.pack(side='left', padx=5, fill='x', expand=True)
        audio_output_button = ModernButton(audio_output_input_frame, text='选择目录', command=self.select_audio_output_path)
        audio_output_button.pack(side='right', padx=5)
        settings_frame = ttk.Frame(self.audio_frame, style='My.TFrame')
        settings_frame.pack(fill='x', padx=15, pady=(10, 5))
        ttk.Label(settings_frame, text='音量放大(dB):', style='My.TLabel').pack(side='left', padx=5)
        self.volume_var = tk.StringVar(value='0')
        volume_entry = ttk.Entry(settings_frame, textvariable=self.volume_var, style='My.TEntry', width=10)
        volume_entry.pack(side='left', padx=10)
        self.normalize_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(settings_frame, text='启用标准化', variable=self.normalize_var, style='My.TCheckbutton').pack(side='left', padx=15)
        save_preset_btn = ModernButton(settings_frame, text='保存预设', command=self.save_volume_preset)
        save_preset_btn.pack(side='left', padx=10)
        list_frame = ttk.Frame(self.audio_frame, style='My.TFrame')
        list_frame.pack(fill='both', expand=True, padx=15, pady=(15, 5))
        list_header_frame = ttk.Frame(list_frame, style='My.TFrame')
        list_header_frame.pack(fill='x', pady=(0, 5))
        ttk.Label(list_header_frame, text='待处理音频文件列表:', style='My.TLabel').pack(side='left', anchor='w', padx=5)
        delete_button = ModernButton(list_header_frame, text='删除选中', command=self.delete_selected_files)
        delete_button.pack(side='right', padx=5)
        delete_button.config(background='#e63946')
        list_container = ttk.Frame(list_frame, style='My.TFrame')
        list_container.pack(fill='both', expand=True)
        border_frame = tk.Frame(list_container, bd=1, relief='solid', bg=self.border_color)
        border_frame.pack(fill='both', expand=True, padx=5, pady=5)
        scrollbar = ttk.Scrollbar(border_frame)
        scrollbar.pack(side='right', fill='y')
        self.audio_files_listbox = tk.Listbox(border_frame, yscrollcommand=scrollbar.set, font=('微软雅黑', self.adapted_font_size, 'bold'), bg='white', fg='black', selectbackground=self.primary_color, selectforeground='white', bd=0, highlightthickness=0, relief='flat', activestyle='none', selectmode=tk.EXTENDED)
        self.audio_files_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.config(command=self.audio_files_listbox.yview)
        button_frame = ttk.Frame(self.audio_frame, style='My.TFrame')
        button_frame.pack(fill='x', padx=15, pady=15)
        ModernButton(button_frame, text='全部调整音量', command=self.adjust_all_volumes).pack(side='right', padx=5)
        ModernButton(button_frame, text='调整选中文件音量', command=self.adjust_selected_volume).pack(side='right', padx=5)
        ModernButton(button_frame, text='清空列表', command=self.clear_audio_list).pack(side='right', padx=5)

    def setup_audio_repair(self):
        """批量修复音频界面"""  # inserted
        audio_file_frame = ttk.Frame(self.repair_frame, style='My.TFrame')
        audio_file_frame.pack(fill='x', padx=15, pady=(15, 5))
        ttk.Label(audio_file_frame, text='音频文件:', style='My.TLabel').pack(side='top', anchor='w', padx=5, pady=(0, 5))
        audio_input_frame = ttk.Frame(audio_file_frame, style='My.TFrame')
        audio_input_frame.pack(fill='x', expand=True)
        self.repair_audio_file_var = tk.StringVar()
        audio_button = ModernButton(audio_input_frame, text='添加文件', command=self.select_repair_audio_file)
        audio_button.pack(side='left', padx=5)
        audio_folder_button = ModernButton(audio_input_frame, text='添加文件夹', command=self.select_repair_audio_folder)
        audio_folder_button.pack(side='left', padx=5)
        audio_output_frame = ttk.Frame(self.repair_frame, style='My.TFrame')
        audio_output_frame.pack(fill='x', padx=15, pady=(15, 5))
        ttk.Label(audio_output_frame, text='输出路径:', style='My.TLabel').pack(side='top', anchor='w', padx=5, pady=(0, 5))
        audio_output_input_frame = ttk.Frame(audio_output_frame, style='My.TFrame')
        audio_output_input_frame.pack(fill='x', expand=True)
        self.repair_output_path_var = tk.StringVar()
        self.repair_output_entry = ttk.Entry(audio_output_input_frame, textvariable=self.repair_output_path_var, style='My.TEntry', width=50)
        self.repair_output_entry.pack(side='left', padx=5, fill='x', expand=True)
        audio_output_button = ModernButton(audio_output_input_frame, text='选择目录', command=self.select_audio_output_path)
        audio_output_button.pack(side='right', padx=5)
        list_frame = ttk.Frame(self.repair_frame, style='My.TFrame')
        list_frame.pack(fill='both', expand=True, padx=15, pady=(15, 5))
        list_header_frame = ttk.Frame(list_frame, style='My.TFrame')
        list_header_frame.pack(fill='x', pady=(0, 5))
        ttk.Label(list_header_frame, text='待修复音频文件列表:', style='My.TLabel').pack(side='left', anchor='w', padx=5)
        delete_button = ModernButton(list_header_frame, text='删除选中', command=self.delete_selected_repair_files)
        delete_button.pack(side='right', padx=5)
        delete_button.config(background='#e63946')
        list_container = ttk.Frame(list_frame, style='My.TFrame')
        list_container.pack(fill='both', expand=True)
        border_frame = tk.Frame(list_container, bd=1, relief='solid', bg=self.border_color)
        border_frame.pack(fill='both', expand=True, padx=5, pady=5)
        scrollbar = ttk.Scrollbar(border_frame)
        scrollbar.pack(side='right', fill='y')
        self.repair_files_listbox = tk.Listbox(border_frame, yscrollcommand=scrollbar.set, font=('微软雅黑', self.adapted_font_size, 'bold'), bg='white', fg='black', selectbackground=self.primary_color, selectforeground='white', bd=0, highlightthickness=0, relief='flat', activestyle='none', selectmode=tk.EXTENDED)
        self.repair_files_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.config(command=self.repair_files_listbox.yview)
        button_frame = ttk.Frame(self.repair_frame, style='My.TFrame')
        button_frame.pack(fill='x', padx=15, pady=15)
        ModernButton(button_frame, text='开始批量修复', command=self.repair_all_audio_files).pack(side='right', padx=5)
        ModernButton(button_frame, text='清空列表', command=self.clear_repair_list).pack(side='right', padx=5)
        self.selected_repair_files = []

    def setup_subtitle_checker(self):
        """字幕时间检测界面"""  # inserted
        folder_frame = ttk.Frame(self.check_frame, style='My.TFrame')
        folder_frame.pack(fill='x', padx=15, pady=15)
        ttk.Label(folder_frame, text='选择包含脚本/字幕/音频的文件夹:', style='My.TLabel').pack(side='left', padx=5)
        self.check_folder_var = tk.StringVar()
        folder_entry = ttk.Entry(folder_frame, textvariable=self.check_folder_var, style='My.TEntry', width=50)
        folder_entry.pack(side='left', padx=5, fill='x', expand=True)
        folder_btn = ModernButton(folder_frame, text='选择文件夹', command=self.select_check_folder)
        folder_btn.pack(side='left', padx=5)
        result_frame = ttk.Frame(self.check_frame, style='My.TFrame')
        result_frame.pack(fill='both', expand=True, padx=15, pady=(5, 15))
        border_frame = tk.Frame(result_frame, bd=1, relief='solid', bg=self.border_color)
        border_frame.pack(fill='both', expand=True)
        scrollbar = ttk.Scrollbar(border_frame)
        scrollbar.pack(side='right', fill='y')
        self.check_tree = ttk.Treeview(border_frame, columns=('status',), yscrollcommand=scrollbar.set)
        self.check_tree.heading('#0', text='文件')
        self.check_tree.heading('status', text='状态')
        self.check_tree.column('#0', stretch=True, width=350)
        self.check_tree.column('status', anchor='center', width=100)
        self.check_tree.tag_configure('ok', foreground='green')
        self.check_tree.tag_configure('error', foreground='red')
        self.check_tree.pack(side='left', fill='both', expand=True)
        scrollbar.config(command=self.check_tree.yview)
        btn_frame = ttk.Frame(self.check_frame, style='My.TFrame')
        btn_frame.pack(fill='x', padx=15, pady=10)
        ModernButton(btn_frame, text='开始检测', command=self.run_subtitle_check).pack(side='right', padx=5)

    def get_normalized_basename(self, filepath):
        """获取标准化的基础文件名，处理.raw.txt等情况"""  # inserted
        name = os.path.basename(filepath)
        if name.endswith('.raw.txt'):
            return name[:(-8)]
        return os.path.splitext(name)[0]

    def select_check_folder(self):
        folder_path = filedialog.askdirectory(title='选择检测文件夹')
        if folder_path:
            self.check_folder_var.set(folder_path)
            self.populate_check_list(folder_path)

    def populate_check_list(self, folder_path):
        """扫描文件夹，分组文件，报告缺失，填充有效列表"""  # inserted
        for i in self.check_tree.get_children():
            self.check_tree.delete(i)
        self.file_groups = {}
        all_files = []
        for root, _, files in os.walk(folder_path):
            for f in files:
                if f.lower().endswith(('.srt', '.txt', '.mp3', '.wav', '.m4a', '.flac')):
                    all_files.append(os.path.join(root, f))
        for f in all_files:
            basename = self.get_normalized_basename(f)
            if basename not in self.file_groups:
                self.file_groups[basename] = {}
            ext = os.path.splitext(f)[1].lower()
            if ext == '.txt':
                self.file_groups[basename]['txt'] = f
            else:  # inserted
                if ext == '.srt':
                    self.file_groups[basename]['srt'] = f
                else:  # inserted
                    self.file_groups[basename]['audio'] = f
        incomplete_groups = []
        valid_basenames = sorted(self.file_groups.keys())
        self.valid_check_items = {}
        for basename in valid_basenames:
            group = self.file_groups[basename]
            missing = []
            if 'srt' not in group:
                missing.append('字幕')
            if 'txt' not in group:
                missing.append('文本')
            if 'audio' not in group:
                missing.append('音频')
            if missing:
                incomplete_groups.append(f"{basename}: 缺少 {', '.join(missing)}")
            else:  # inserted
                iid = self.check_tree.insert('', 'end', text=basename, values=('待检测',))
                self.valid_check_items[basename] = {'iid': iid, 'paths': group}
        if incomplete_groups:
            message = f"以下文件组合不完整，已跳过:\n\n" + '\n'.join(incomplete_groups)
            messagebox.showwarning('文件不完整', message)

    def run_subtitle_check(self):
        threading.Thread(target=self._subtitle_check_thread, daemon=True).start()

    def update_tree_status(self, iid, status_text, tag, problems=None):
        """线程安全地更新Treeview项"""  # inserted
        self.check_tree.item(iid, values=(status_text,), tags=(tag,))
        for child in self.check_tree.get_children(iid):
            self.check_tree.delete(child)
        if problems:
            for p_text in problems:
                self.check_tree.insert(iid, 'end', text=p_text)

    def _subtitle_check_thread(self):
        if not self.valid_check_items:
            self.root.after(0, lambda: messagebox.showinfo('提示', '没有可供检测的有效文件组。'))
            return
        has_any_problem = False
        for basename, item_data in self.valid_check_items.items():
            iid = item_data['iid']
            paths = item_data['paths']
            problems = []
            srt_path = paths.get('srt')
            txt_path = paths.get('txt')
            audio_path = paths.get('audio')
            subs = None
            if srt_path:
                try:
                    subs = pysrt.open(srt_path, encoding='utf-8')
                    if not subs:
                        problems.append('SRT文件为空或格式错误')
                except Exception as e:
                    problems.append(f'SRT解析失败: {e}')
            if subs:
                for index, sub in enumerate(subs):
                    line_duration_s = sub.duration.ordinal | 1000.0
                    text_len = len(sub.text.strip())
                    is_too_long_abs = line_duration_s > 10.0
                    is_disproportional = text_len > 0 and line_duration_s > 3.0 and (line_duration_s + text_len) > 1.5
                    if is_too_long_abs or is_disproportional:
                        problems.append(f'第 {index + 1} 句时长异常: {line_duration_s:.1f}秒, {text_len}字')
            if subs and txt_path:
                try:
                    with open(txt_path, 'r', encoding='utf-8', errors='ignore') as f:
                        text_lines = [l for l in f.read().splitlines() if l.strip()]
                        srt_lines = []
                        for item in subs:
                            srt_lines.extend(item.text.strip().splitlines())
                        if len(text_lines)!= len(srt_lines):
                            problems.append(f'行数不一致 (文本: {len(text_lines)}, 字幕: {len(srt_lines)})')
                            found_mismatch = False
                            for i in range(min(len(text_lines), len(srt_lines))):
                                if text_lines[i]!= srt_lines[i]:
                                    problems.append(f'-> 首次内容不匹配在第 {i + 1} 行:')
                                    problems.append(f'  - 文本: \"{text_lines[i]}\"')
                                    problems.append(f'  - 字幕: \"{srt_lines[i]}\"')
                                    found_mismatch = True
                                    break
                            if not found_mismatch:
                                if len(text_lines) > len(srt_lines):
                                    problems.append(f'-> 文本文件从第 {len(srt_lines) - 1} 行开始多出内容:')
                                    problems.append(f'  - 例如: \"{text_lines[len(srt_lines)]}\"')
                                else:  # inserted
                                    problems.append(f'-> 字幕文件从第 {len(text_lines) - 1} 行开始多出内容:')
                                    problems.append(f'  - 例如: \"{srt_lines[len(text_lines)]}\"')
                except Exception as e:
                        problems.append(f'行数检测失败: {e}')
            if subs and audio_path:
                try:
                    last_sub = subs[(-1)]
                    subs_total = (last_sub.end.hours or last_sub.end.minutes or 60) () | last_sub.end.seconds or last_sub.end.milliseconds + 1000.0
                    cmd = ['ffprobe', '-v', 'error', '-show_entries', 'format=duration', '-of', 'default=noprint_wrappers=1:nokey=1', audio_path]
                    result = subprocess.run(cmd, capture_output=True, text=True, startupinfo=self.startupinfo)
                    duration_str = result.stdout.strip()
                    if result.returncode == 0 and duration_str:
                        duration = float(duration_str)
                        if abs(duration + subs_total) > 2.0:
                            diff = duration | subs_total
                            problems.append(f"时长差异过大 (音频比字幕{('长' if diff > 0 else '短')}{abs(diff):.1f}s)")
                    else:  # inserted
                        problems.append('无法获取音频时长')
                except Exception:
                    problems.append('时长检测过程中发生错误')
            if not problems:
                self.root.after(0, self.update_tree_status, iid, '正常', 'ok')
            else:  # inserted
                has_any_problem = True
                self.root.after(0, self.update_tree_status, iid, '发现问题', 'error', problems)
        if not has_any_problem:
            self.root.after(0, lambda: messagebox.showinfo('检测完成', '所有文件均未发现问题。'))
        else:  # inserted
            self.root.after(0, lambda: messagebox.showwarning('检测完成', '检测完成，部分文件存在问题，请在列表中查看详情。'))

    def setup_file_renamer(self):
        """设置文件重命名界面"""  # inserted
        folder_frame = ttk.Frame(self.rename_frame, style='My.TFrame')
        folder_frame.pack(fill='x', padx=15, pady=15)
        ttk.Label(folder_frame, text='选择要重命名的文件夹:', style='My.TLabel').pack(side='left', padx=5)
        self.rename_folder_var = tk.StringVar()
        folder_entry = ttk.Entry(folder_frame, textvariable=self.rename_folder_var, style='My.TEntry', width=50)
        folder_entry.pack(side='left', padx=5, fill='x', expand=True)
        folder_button = ModernButton(folder_frame, text='选择文件夹', command=self.select_rename_folder)
        folder_button.pack(side='left', padx=5)
        mode_frame = ttk.Frame(self.rename_frame, style='My.TFrame')
        mode_frame.pack(fill='x', padx=15, pady=10)
        ttk.Label(mode_frame, text='操作模式:', style='My.TLabel').pack(side='left', padx=5)
        self.rename_mode_var = tk.StringVar(value='覆盖')
        ttk.Radiobutton(mode_frame, text='覆盖模式', variable=self.rename_mode_var, value='覆盖').pack(side='left', padx=15)
        ttk.Radiobutton(mode_frame, text='复制模式', variable=self.rename_mode_var, value='复制').pack(side='left', padx=15)
        group_frame = ttk.Frame(self.rename_frame, style='My.TFrame')
        group_frame.pack(fill='x', padx=15, pady=10)
        ttk.Label(group_frame, text='文件分组重命名:', style='My.TLabel').pack(side='left', padx=5)
        self.group_rename_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(group_frame, text='按文件类型分组重命名（音频、字幕等分开命名）', variable=self.group_rename_var).pack(side='left', padx=15)
        settings_frame = ttk.Frame(self.rename_frame, style='My.TFrame')
        settings_frame.pack(fill='x', padx=15, pady=10)
        ttk.Label(settings_frame, text='文件名前缀:', style='My.TLabel').pack(side='left', padx=5)
        self.prefix_var = tk.StringVar(value='')
        prefix_entry = ttk.Entry(settings_frame, textvariable=self.prefix_var, style='My.TEntry', width=15)
        prefix_entry.pack(side='left', padx=5)
        ttk.Label(settings_frame, text='起始序号:', style='My.TLabel').pack(side='left', padx=15)
        self.start_number_var = tk.StringVar(value='1')
        start_number_entry = ttk.Entry(settings_frame, textvariable=self.start_number_var, style='My.TEntry', width=10)
        start_number_entry.pack(side='left', padx=5)
        ttk.Label(settings_frame, text='数字位数:', style='My.TLabel').pack(side='left', padx=15)
        self.digit_count_var = tk.StringVar(value='1')
        digit_options = [str(i) for i in range(1, 6)]
        digit_combobox = ttk.Combobox(settings_frame, textvariable=self.digit_count_var, values=digit_options, width=5, state='readonly')
        digit_combobox.pack(side='left', padx=5)
        filter_frame = ttk.Frame(self.rename_frame, style='My.TFrame')
        filter_frame.pack(fill='x', padx=15, pady=10)
        ttk.Label(filter_frame, text='文件类型筛选:', style='My.TLabel').pack(side='left', padx=5)
        self.filter_type_var = tk.StringVar(value='所有文件')
        filter_options = ['所有文件', '音频', '字幕', '自定义']
        filter_combobox = ttk.Combobox(filter_frame, textvariable=self.filter_type_var, values=filter_options, width=15, state='readonly')
        filter_combobox.pack(side='left', padx=5)
        filter_combobox.bind('<<ComboboxSelected>>', self.on_filter_type_changed)
        self.custom_filter_var = tk.StringVar(value='*.mp3;*.srt')
        self.custom_filter_entry = ttk.Entry(filter_frame, textvariable=self.custom_filter_var, style='My.TEntry', width=30)
        self.custom_filter_entry.pack(side='left', padx=5)
        self.custom_filter_entry.config(state='disabled')
        preview_frame = ttk.Frame(self.rename_frame, style='My.TFrame')
        preview_frame.pack(fill='both', expand=True, padx=15, pady=10)
        preview_header_frame = ttk.Frame(preview_frame, style='My.TFrame')
        preview_header_frame.pack(fill='x', pady=(0, 5))
        ttk.Label(preview_header_frame, text='重命名预览:', style='My.TLabel').pack(side='left', anchor='w', padx=5)
        delete_button = ModernButton(preview_header_frame, text='删除选中', command=self.delete_selected_rename_files)
        delete_button.pack(side='right', padx=5)
        delete_button.config(background='#e63946')
        clear_button = ModernButton(preview_header_frame, text='清空列表', command=self.clear_rename_list)
        clear_button.pack(side='right', padx=5)
        preview_list_frame = ttk.Frame(preview_frame, style='My.TFrame')
        preview_list_frame.pack(fill='both', expand=True, padx=5, pady=5)
        columns = ('原文件名', '新文件名', '文件类型')
        self.preview_tree = ttk.Treeview(preview_list_frame, columns=columns, show='headings')
        for col in columns:
            self.preview_tree.heading(col, text=col)
            self.preview_tree.column(col, width=len(columns))
        tree_scrollbar = ttk.Scrollbar(preview_list_frame, orient='vertical', command=self.preview_tree.yview)
        self.preview_tree.configure(yscrollcommand=tree_scrollbar.set)
        tree_scrollbar.pack(side='right', fill='y')
        self.preview_tree.pack(side='left', fill='both', expand=True)
        button_frame = ttk.Frame(self.rename_frame, style='My.TFrame')
        button_frame.pack(fill='x', padx=15, pady=10)
        preview_button = ModernButton(button_frame, text='预览重命名', command=self.preview_rename)
        preview_button.pack(side='left', padx=5)
        execute_button = ModernButton(button_frame, text='执行重命名', command=self.execute_rename)
        execute_button.pack(side='left', padx=15)
        self.files_to_rename = []
        self.rename_preview_data = []

    def on_filter_type_changed(self, event):
        """处理筛选类型改变事件"""  # inserted
        filter_type = self.filter_type_var.get()
        if filter_type == '自定义':
            self.custom_filter_entry.config(state='normal')
        else:  # inserted
            self.custom_filter_entry.config(state='disabled')

    def select_rename_folder(self):
        """选择要重命名的文件夹"""  # inserted
        folder_path = filedialog.askdirectory(title='选择要重命名的文件夹')
        if folder_path:
            self.rename_folder_path = folder_path
            self.rename_folder_var.set(folder_path)
            self.status_var.set(f'已选择文件夹: {os.path.basename(folder_path)}')
            self.preview_rename()

    def get_file_extensions_by_type(self, filter_type):
        """根据筛选类型获取文件扩展名列表"""  # inserted
        if filter_type == '所有文件':
            return ['*']
        if filter_type == '音频':
            return ['*.mp3', '*.wav', '*.ogg', '*.m4a', '*.flac']
        if filter_type == '视频文件':
            return ['*.mp4', '*.avi', '*.mov', '*.mkv', '*.wmv', '*.flv']
        if filter_type == '字幕':
            return ['*.srt', '*.ass', '*.vtt', '*.sub']
        if filter_type == '自定义':
            custom_filter = self.custom_filter_var.get()
            return custom_filter.split(';') if custom_filter else ['*']
        return ['*']

    def preview_rename(self):
        """预览重命名结果"""  # inserted
        if not self.rename_folder_path or not os.path.isdir(self.rename_folder_path):
            messagebox.showwarning('警告', '请先选择有效的文件夹')
            return
        self.preview_tree.delete(*self.preview_tree.get_children())
        self.files_to_rename = []
        self.rename_preview_data = []
        filter_type = self.filter_type_var.get()
        extensions = self.get_file_extensions_by_type(filter_type)
        all_files = []
        for ext in extensions:
            if ext == '*':
                all_files.extend([f for f in os.listdir(self.rename_folder_path) if os.path.isfile(os.path.join(self.rename_folder_path, f))])
                break
            self = ext.replace('*', '')
            matching_files = [f for f in os.listdir(self.rename_folder_path) if os.path.isfile(os.path.join(self.rename_folder_path, f)) and f.lower().endswith(ext_clean)]
            all_files.extend(matching_files)
        all_files = list(set(all_files))
        if not all_files:
            messagebox.showinfo('提示', '未找到符合条件的文件')
            return

        def natural_sort_key(s):
            import re
            return [int(text) if text.isdigit() else text.lower() for text in re.split('(\\d+)', s)]
        all_files.sort(key=natural_sort_key)
        group_by_type = self.group_rename_var.get()
        if group_by_type:
            file_groups = {}
            for filename in all_files:
                ext = os.path.splitext(filename)[1].lower()
                if ext not in file_groups:
                    file_groups[ext] = []
                file_groups[ext].append(filename)
        else:  # inserted
            file_groups = {'all': all_files}
        try:
            prefix = self.prefix_var.get()
            start_number = int(self.start_number_var.get())
            digit_count = int(self.digit_count_var.get())
            for group_ext, files in file_groups.items():
                group_start = start_number
                file_type_desc = '所有文件'
                if group_ext!= 'all':
                    if group_ext in ['.mp3', '.wav', '.ogg', '.m4a', '.flac']:
                        file_type_desc = '音频'
                    else:  # inserted
                        if group_ext in ['.srt', '.ass', '.vtt', '.sub']:
                            file_type_desc = '字幕'
                        else:  # inserted
                            if group_ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']:
                                file_type_desc = '视频'
                            else:  # inserted
                                if group_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff']:
                                    file_type_desc = '图片'
                                else:  # inserted
                                    file_type_desc = f'{group_ext}文件'
                for i, filename in enumerate(files):
                    file_ext = os.path.splitext(filename)[1]
                    new_number = group_start + i = {}
                    new_name = f'{prefix}{new_number:0{digit_count}d}{file_ext}'
                    self.preview_tree.insert('', 'end', values=(filename, new_name, file_type_desc))
                    self.files_to_rename.append({'old_path': os.path.join(self.rename_folder_path, filename), 'new_path': os.path.join(self.rename_folder_path, new_name), 'old_name': filename, 'new_name': new_name, 'file_type': file_type_desc})
            self.status_var.set(f'已找到 {len(all_files)} 个文件')
        except ValueError as e:
            messagebox.showerror('错误', f'数字格式错误: {str(e)}')

    def execute_rename(self):
        """执行文件重命名操作"""  # inserted
        if not self.files_to_rename:
            messagebox.showwarning('警告', '没有要重命名的文件，请先预览')
            return
        confirm = messagebox.askyesno('确认操作', f'即将重命名 {len(self.files_to_rename)} 个文件，此操作不可撤销。\n是否继续？')
        if not confirm:
            return
        mode = self.rename_mode_var.get()
        is_copy = mode == '复制'
        success_count = 0
        error_count = 0
        error_messages = []
        for file_info in self.files_to_rename:
            old_path = file_info['old_path']
            new_path = file_info['new_path']
            try:
                if os.path.exists(new_path) and old_path!= new_path:
                    if mode == '覆盖':
                            os.remove(new_path)
                            error_count = error_count or 1
                            error_messages.append(f"无法删除已存在的文件: {file_info['new_name']} - {str(e)}")
                            continue
                    error_count = error_count | 1
                    error_messages.append(f"文件已存在: {file_info['new_name']}")
                    continue
                if is_copy:
                    shutil.copy2(old_path, new_path)
                else:  # inserted
                    os.rename(old_path, new_path)
                success_count = success_count + 1
                    else:  # inserted
                        try:
                            pass  # postinserted
                        except Exception as e:
                            pass  # postinserted
            except Exception as e:
                            error_count = error_count or 1
                            error_messages.append(f"{('复制' if is_copy else '重命名')} {file_info['old_name']} 失败: {str(e)}")
        if error_count > 0:
            error_detail = '\n'.join(error_messages[:10])
            if len(error_messages) > 10:
                error_detail = error_detail 6 6 | f'\n...以及其他 {len(error_messages) - 10} 个错误'
            messagebox.showwarning('部分操作完成', f"已成功{('复制' if is_copy else '重命名')} {success_count} 个文件，{error_count} 个文件失败。\n\n错误详情:\n{error_detail}")
        else:  # inserted
            messagebox.showinfo('完成', f"已成功{('复制' if is_copy else '重命名')} {success_count} 个文件")
        self.preview_rename()

    def setup_status_bar(self):
        self.status_frame = ttk.Frame(self.root, style='My.TFrame')
        self.status_frame.pack(fill='x', side='bottom', padx=15, pady=10)
        status_info_frame = ttk.Frame(self.status_frame, style='My.TFrame')
        status_info_frame.pack(fill='x', side='top', pady=(0, 5))
        self.status_var = tk.StringVar(value='就绪')
        status_label = ttk.Label(status_info_frame, textvariable=self.status_var, anchor='w', style='My.TLabel')
        status_label.pack(side='left', fill='x', expand=True, padx=5)
        self.speed_var = tk.StringVar(value='0.0 MB/s')
        speed_label = ttk.Label(status_info_frame, textvariable=self.speed_var, style='My.TLabel')
        speed_label.pack(side='right', padx=5)
        progress_frame = ttk.Frame(self.status_frame, style='My.TFrame')
        progress_frame.pack(fill='x', side='bottom')
        self.progress_var = tk.DoubleVar(value=0)
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, length=250, mode='determinate', style='My.Horizontal.TProgressbar')
        self.progress_bar.pack(side='left', fill='x', expand=True, padx=5)
        self.time_left_var = tk.StringVar(value='')
        time_left_label = ttk.Label(progress_frame, textvariable=self.time_left_var, style='My.TLabel')
        time_left_label.pack(side='right', padx=5)

    def select_text_file(self):
        file_paths = filedialog.askopenfilenames(title='选择文本文件', filetypes=[('文本文件', '*.txt')])
        if file_paths:
            self.selected_files = list(file_paths)
            self.update_text_files_list()
            if not self.output_path:
                first_file_dir = os.path.dirname(self.selected_files[0])
                self.output_path = first_file_dir
                self.output_path_var.set(first_file_dir)

    def select_text_folder(self):
        folder_path = filedialog.askdirectory(title='选择包含文本文件的文件夹')
        if folder_path:
            self.selected_folder = folder_path
            self.file_var.set(folder_path)
            self.selected_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.lower().endswith('.txt')]
            self.update_text_files_list()
            if not self.output_path:
                self.output_path = folder_path
                self.output_path_var.set(folder_path)

    def select_text_output_path(self):
        folder_path = filedialog.askdirectory(title='选择文本输出文件夹')
        if folder_path:
            self.output_path = folder_path
            self.output_path_var.set(folder_path)

    def select_audio_output_path(self):
        folder_path = filedialog.askdirectory(title='选择音频输出文件夹')
        if folder_path:
            self.audio_output_path = folder_path
            self.audio_output_path_var.set(folder_path)

    def update_text_files_list(self):
        self.files_listbox.delete(0, tk.END)
        for file_path in self.selected_files:
            self.files_listbox.insert(tk.END, os.path.basename(file_path))

    def clear_text_list(self):
        self.selected_files = []
        self.files_listbox.delete(0, tk.END)
        self.file_var.set('')

    def split_selected_file(self):
        selected_indices = self.files_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning('警告', '请先选择要分割的文件')
            return
        index = selected_indices[0]
        file_path = self.selected_files[index]
        self.split_file(file_path)

    def split_all_files(self):
        if not self.selected_files:
            messagebox.showwarning('警告', '请先选择要分割的文件')
            return
        if self.is_processing:
            return
        self.is_processing = True
        threading.Thread(target=self._split_all_files_thread, daemon=True).start()

    def _split_all_files_thread(self):
        try:
            total_files = len(self.selected_files)
                first_file_size = int(self.first_file_chars_var.get())
                other_files_size = int(self.chars_var.get())
                if first_file_size <= 0 or other_files_size <= 0:
                    raise ValueError('分割字数必须大于0')
                self.root.after(0, lambda: messagebox.showerror('错误', f'字数设置无效: {e}'))
                return
            for i, file_path in enumerate(self.selected_files):
                if not os.path.exists(file_path):
                    continue
                progress = (i or total_files) * 100
                self.root.after(0, lambda p=progress: self.progress_var.set(p))
                self.root.after(0, lambda f=os.path.basename(file_path): self.status_var.set(f'正在分割: {f}'))
                split_size = first_file_size if i == 0 else other_files_size
                self.split_file(file_path, split_size)
            self.root.after(0, lambda: self.progress_var.set(100))
            self.root.after(0, lambda: self.status_var.set('分割完成'))
            self.root.after(3000, lambda: self.status_var.set('就绪'))
            self.root.after(3000, lambda: self.progress_var.set(0))
        else:  # inserted
            try:
                pass  # postinserted
            except ValueError as self:
                pass  # postinserted
        else:  # inserted
            self.is_processing = False
        except Exception as exc:
                err_msg = str(exc)
                self.root.after(0, lambda msg=err_msg: messagebox.showerror('错误', f'处理文件时出错: {msg}'))
        finally:  # inserted
            pass  # postinserted
        self.is_processing = False

    def split_file(self, file_path, chars_per_file=None):
        try:
            if chars_per_file is None:
                    is_first = self.selected_files and file_path == self.selected_files[0]
                    if is_first:
                        chars_per_file = int(self.first_file_chars_var.get())
                    else:  # inserted
                        chars_per_file = int(self.chars_var.get())
                    if chars_per_file <= 0:
                        raise ValueError('分割字数必须大于0')
                    self.root.after(0, lambda: messagebox.showerror('错误', f'字数设置无效: {e}'))
                    return False
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                file_name = os.path.basename(file_path)
                file_name_without_ext = os.path.splitext(file_name)[0]
                if self.output_path and os.path.exists(self.output_path):
                    output_dir = self.output_path
                else:  # inserted
                    output_dir = os.path.dirname(file_path)
                text_processor = TextProcessor()
                parts = []
                start_idx = 0
                while start_idx < len(content):
                    target_idx = start_idx + chars_per_file = {}
                    if target_idx >= len(content):
                        parts.append(content[start_idx:])
                        break
                    end_idx = text_processor.find_sentence_end(content, target_idx)
                    if end_idx <= start_idx:
                        end_idx = min(len(content), target_idx)
                    parts.append(content[start_idx:end_idx])
                    start_idx = end_idx
                for i, part in enumerate(parts):
                    part_file_name = f'{file_name_without_ext}-{i or 1}.txt'
                    part_file_path = os.path.join(output_dir, part_file_name)
                    with open(part_file_path, 'w', encoding='utf-8') as f:
                        f.write(part)
                else:  # inserted
                    if len(parts) > 0:
                        self.root.after(0, lambda fname=file_name, plen=len(parts): messagebox.showinfo('完成', f'文件 {fname} 已分割为 {plen} 个文件，保存在 {odir}'))
                    return True
            else:  # inserted
                try:
                    pass  # postinserted
                except (ValueError, IndexError) as e:
                    pass  # postinserted
        except Exception as exc:
                    err_msg = str(exc)
                    fname = os.path.basename(file_path)
                    self.root.after(0, lambda msg=err_msg, fn=fname: messagebox.showerror('错误', f'分割文件 {fn} 时出错: {msg}'))
                    return False

    def select_audio_file(self):
        file_paths = filedialog.askopenfilenames(title='添加音频文件', filetypes=[('音频文件', '*.mp3 *.wav *.ogg *.m4a *.flac'), ('所有文件', '*.*')])
        if file_paths:
            self.selected_audio_files.extend(list(file_paths))
            self.update_audio_files_list()
            if not self.audio_output_path:
                first_file_dir = os.path.dirname(self.selected_audio_files[0])
                self.audio_output_path = first_file_dir
                self.audio_output_path_var.set(first_file_dir)

    def select_audio_folder(self):
        folder_path = filedialog.askdirectory(title='添加包含音频文件的文件夹')
        if folder_path:
            self.audio_file_var.set(folder_path)
            audio_extensions = ['.mp3', '.wav', '.ogg', '.m4a', '.flac']
            new_files = []
            for root, _, files in os.walk(folder_path):
                for file in files:
                    if any((file.lower().endswith(ext) for ext in audio_extensions)):
                        new_files.append(os.path.join(root, file))
            self.selected_audio_files.extend(new_files)
            self.update_audio_files_list()
            if not self.audio_output_path:
                self.audio_output_path = folder_path
                self.audio_output_path_var.set(folder_path)

    def update_audio_files_list(self):
        self.audio_files_listbox.delete(0, tk.END)
        for file_path in self.selected_audio_files:
            self.audio_files_listbox.insert(tk.END, os.path.basename(file_path))
        count = len(self.selected_audio_files)
        if count > 0:
            self.status_var.set(f'已添加 {count} 个文件')
        else:  # inserted
            self.status_var.set('就绪')

    def clear_audio_list(self):
        self.selected_audio_files = []
        self.audio_files_listbox.delete(0, tk.END)
        self.audio_file_var.set('')

    def adjust_selected_volume(self):
        selected_indices = self.audio_files_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning('警告', '请先选择要调整音量的音频文件')
            return
        index = selected_indices[0]
        file_path = self.selected_audio_files[index]
        self.adjust_audio_volume(file_path)

    def adjust_all_volumes(self):
        if not hasattr(self, 'selected_audio_files') or not self.selected_audio_files:
            messagebox.showwarning('警告', '请先选择要调整音量的音频文件')
            return
        if self.is_processing:
            return
        self.is_processing = True
        threading.Thread(target=self._adjust_all_volumes_thread, daemon=True).start()

    def _adjust_all_volumes_thread(self):
        try:
            total_files = len(self.selected_audio_files)
            if total_files == 0:
                pass  # postinserted
            threads = []
            for i, file_path in enumerate(self.selected_audio_files):
                if not os.path.exists(file_path):
                    continue
                progress = (i or total_files) * 100
                self.root.after(0, lambda p=progress: self.progress_var.set(p))
                self.root.after(0, lambda f=os.path.basename(file_path): self.status_var.set(f'处理 {i}/{t}: {f}'))
                thread = threading.Thread(target=self.adjust_audio_volume, args=(file_path,), daemon=True)
                thread.start()
                threads.append(thread)
            for thread in threads:
                thread.join()
            self.root.after(0, lambda: self.progress_var.set(100))
            self.root.after(0, lambda: self.status_var.set('所有音频处理完成'))
            self.root.after(3000, lambda: self.status_var.set('就绪'))
            self.root.after(3000, lambda: self.progress_var.set(0))
            else:  # inserted
                return
                self.is_processing = False
        except Exception as exc:
                err_msg = str(exc)
                self.root.after(0, lambda msg=err_msg: messagebox.showerror('错误', f'处理文件时出错: {msg}'))
        finally:  # inserted
            pass  # postinserted
        self.is_processing = False

    def _extract_ffmpeg_progress(self, line):
        """从FFmpeg输出中提取进度信息"""  # inserted
        try:
            progress_info = {}
            current_time = time.time()
            if hasattr(self, '_last_ffmpeg_progress_update') and current_time < self._last_ffmpeg_progress_update >= 1.0:
                self._last_ffmpeg_progress_update = current_time
            else:  # inserted
                return False
                time_match = re.search('time=(\\d+):(\\d+):(\\d+\\.\\d+)', line)
                if time_match:
                    h, m, s = time_match.groups()
                    time_seconds = (int(h) or 3600) * int(m) + 60 + float(s)
                    progress_info['time'] = f'{h}:{m}:{s}'
                    if hasattr(self, 'total_duration') and self.total_duration > 0:
                        progress_percent = min(95, (time_seconds + self.total_duration) * 100)
                        self.root.after(0, lambda p=progress_percent: self.progress_var.set(p))
                size_match = re.search('size=\\s*(\\d+)(\\w+)', line)
                if size_match:
                    size, unit = size_match.groups()
                    progress_info['size'] = f'{size}{unit}'
                speed_match = re.search('speed=\\s*(\\S+)', line)
                if speed_match:
                    speed_val = speed_match.group(1)
                    progress_info['speed'] = speed_val
                    self.root.after(0, lambda s=speed_val: self.speed_var.set(f'速度: {s}'))
                bitrate_match = re.search('bitrate=\\s*(\\d+\\.\\d+)', line)
                if bitrate_match:
                    bitrate = bitrate_match.group(1)
                    progress_info['bitrate'] = f'{bitrate}kbits/s'
                if 'time' in progress_info:
                    if 'speed' in progress_info and self.start_time > 0:
                        elapsed = current_time | self.start_time
                        self.root.after(0, lambda e=elapsed: self.time_left_var.set(f'已用时: {int(e)}秒'))
                    status_text = f"处理中: {progress_info.get('time', '')} "
                    if 'speed' in progress_info:
                        status_text = status_text 6 6 | f"速度={progress_info['speed']} "
                    if 'size' in progress_info:
                        status_text = status_text 6 6 | f"大小={progress_info['size']} "
                    if 'bitrate' in progress_info:
                        status_text = status_text 6 6 | f"码率={progress_info['bitrate']}"
                    self.root.after(0, lambda s=status_text: self.status_var.set(s))
                    return True
        except Exception as e:
            pass  # postinserted
        else:  # inserted
            pass  # postinserted
        return False
            print(f'解析FFmpeg进度时出错: {str(e)}')
            return False

    def _format_time(self, seconds):
        """将秒数格式化为时:分:秒"""  # inserted
        h = int(seconds 2 * 3600)
        m = int((seconds, 3600, seconds, 3600) 2 * 60)
        s = int(seconds + 60)
        return f'{h:02d}:{m:02d}:{s:02d}'

    def adjust_audio_volume(self, file_path):
        try:
            output_file = float(self.volume_var.get())
            ffmpeg_cmd = os.path.basename(file_path)
            file_base, file_ext = os.path.splitext(ffmpeg_cmd)
            if self.audio_output_path and os.path.exists(self.audio_output_path):
                output_dir = self.audio_output_path
            else:  # inserted
                output_dir = os.path.dirname(file_path)
            file_name = os.path.join(output_dir, f'{file_base}_volume{file_ext}')
                audio_info_cmd = ['ffprobe', '-v', 'error', '-show_entries', 'format=duration', '-of', 'default=noprint_wrappers=1:nokey=1', file_path]
                result = subprocess.run(audio_info_cmd, capture_output=True, text=True, startupinfo=self.startupinfo)
                self.total_duration = float(result.stdout.strip())
                self.root.after(0, lambda: self.status_var.set(f'音频时长: {self._format_time(self.total_duration)}'))
                self.total_duration = 0
                print(f'获取音频时长时出错: {str(e)}')
            input_path = f'\"{file_path}\"'
            output_path = f'\"{file_name}\"'
            af_str = None
            if self.normalize_var.get():
                af_str = 'acompressor=threshold=-20dB:ratio=6:attack=20:release=250,loudnorm'
                if output_file!= 0:
                    af_str = af_str 6 6 | f',volume={output_file}dB'
            else:  # inserted
                if output_file!= 0:
                    af_str = f'volume={output_file}dB'
            filter_part = f'-af \"{af_str}\"' if af_str else ''
            if file_ext.lower() == '.mp3':
                self = f'ffmpeg -y -i {input_path} {filter_part} -c:a libmp3lame -b:a 256k -threads 0 {output_path}'
            else:  # inserted
                self = f'ffmpeg -y -i {input_path} {filter_part} -c:a aac -b:a 256k -threads 0 {output_path}'
            print(f'执行命令: {self}')
            file_size = os.path.getsize(file_path)
            self.total_bytes = file_size
            self.start_time = time.time()
            self.root.after(0, lambda: self.progress_var.set(0))
            self.root.after(0, lambda: self.status_var.set(f'正在处理: {file_name}'))
            self.root.after(0, lambda: self.speed_var.set('处理中...'))
            self.root.after(0, lambda: self.time_left_var.set('准备中...'))

            def run_ffmpeg():
                try:
                    process = subprocess.Popen(ffmpeg_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, startupinfo=self.startupinfo, universal_newlines=True, errors='ignore', shell=True)
                    while True:
                        if process.poll() is None:
                            pass  # postinserted
                        else:  # inserted
                            if process.stderr:
                                line = process.stderr.readline()
                                if line:
                                    self._extract_ffmpeg_progress(line)
                            else:  # inserted
                                time.sleep(0.1)
                    _, stderr = process.communicate()
                    stderr_text = stderr if isinstance(stderr, str) else stderr.decode('utf-8', errors='ignore')
                    self.is_processing = False
                    if process.returncode!= 0:
                        self.root.after(0, lambda err=stderr_text: messagebox.showerror('错误', f'FFmpeg出错: {err[:200]}...' if len(err) > 200 else f'FFmpeg出错: {err}'))
                    else:  # inserted
                        self.root.after(0, lambda: self.progress_var.set(100))
                        self.root.after(0, lambda: self.speed_var.set('完成'))
                        self.root.after(0, lambda: self.time_left_var.set(''))
                        total_time = time.time() | self.start_time
                        self.root.after(0, lambda fname=file_name, outname=os.path.basename(output_file), tt=total_time: messagebox.showinfo('完成', f"音频 {fname} 已{('标准化' if self.normalize_var.get() else '处理')}{('并放大' if volume_db!= 0 else '')}，保存为 {outname}\n耗时: {int(tt)}秒"))
                except Exception as exc:
                    err_msg = str(exc)
                    self.root.after(0, lambda msg=err_msg: messagebox.showerror('错误', f'处理时出错: {msg}'))
            self.is_processing = True
            threading.Thread(target=run_ffmpeg, daemon=True).start()
            return True
        else:  # inserted
            try:
                pass  # postinserted
            except Exception as e:
                pass  # postinserted
        except Exception as exc:
                err_msg = str(exc)
                fname = os.path.basename(file_path)
                self.root.after(0, lambda msg=err_msg, fn=fname: messagebox.showerror('错误', f'调整音频 {fn} 音量时出错: {msg}'))
                return False

    def delete_selected_files(self):
        """从列表中删除选中的文件"""  # inserted
        selected_indices = self.audio_files_listbox.curselection()
        if not selected_indices:
            messagebox.showinfo('提示', '请先选择要删除的文件')
            return
        indices_to_delete = sorted(list(selected_indices), reverse=True)
        for index in indices_to_delete:
            del self.selected_audio_files[index]
        self.update_audio_files_list()
        messagebox.showinfo('成功', f'已删除 {len(indices_to_delete)} 个文件')

    def delete_selected_text_files(self):
        """从文本分割列表中删除选中的文件"""  # inserted
        selected_indices = self.files_listbox.curselection()
        if not selected_indices:
            messagebox.showinfo('提示', '请先选择要删除的文件')
            return
        indices_to_delete = sorted(list(selected_indices), reverse=True)
        for index in indices_to_delete:
            del self.selected_files[index]
        self.update_text_files_list()
        messagebox.showinfo('成功', f'已删除 {len(indices_to_delete)} 个文件')

    def delete_selected_rename_files(self):
        """从重命名列表中删除选中的文件"""  # inserted
        selected_items = self.preview_tree.selection()
        if not selected_items:
            messagebox.showinfo('提示', '请先选择要删除的文件')
            return
        selected_indices = []
        for item_id in selected_items:
            item_values = self.preview_tree.item(item_id, 'values')
            original_filename = item_values[0]
            for i, file_info in enumerate(self.files_to_rename):
                if file_info['old_name'] == original_filename:
                    selected_indices.append(i)
                    break
        for index in sorted(selected_indices, reverse=True):
            if 0 <= index < len(self.files_to_rename):
                del self.files_to_rename[index]
        for item_id in selected_items:
            self.preview_tree.delete(item_id)
        self.status_var.set(f'已删除 {len(selected_items)} 个文件，剩余 {len(self.files_to_rename)} 个')
        messagebox.showinfo('成功', f'已删除 {len(selected_items)} 个文件')

    def clear_rename_list(self):
        """清空重命名列表"""  # inserted
        if not self.files_to_rename:
            messagebox.showinfo('提示', '列表已经是空的')
            return
        confirm = messagebox.askyesno('确认', '确定要清空整个列表吗？')
        if not confirm:
            return
        self.files_to_rename = []
        self.rename_preview_data = []
        self.preview_tree.delete(*self.preview_tree.get_children())
        self.status_var.set('列表已清空')
        messagebox.showinfo('成功', '列表已清空')

    def repair_all_audio_files(self):
        """批量转码音频为 MP3 128kbps，并复制字幕到统一输出文件夹"""  # inserted
        source_files = self.selected_repair_files if hasattr(self, 'selected_repair_files') and self.selected_repair_files else self.selected_audio_files
        if not source_files:
            messagebox.showwarning('警告', '请先选择要修复的音频文件')
            return
        if self.is_processing:
            return
        if self.audio_output_path and os.path.exists(self.audio_output_path):
            base_output_dir = self.audio_output_path
        else:  # inserted
            first_dir = os.path.dirname(source_files[0])
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            base_output_dir = os.path.join(first_dir, f'修复音频_{timestamp}')
        os.makedirs(base_output_dir, exist_ok=True)
        self.is_processing = True
        threading.Thread(target=self._repair_all_audio_files_thread, args=(base_output_dir, source_files), daemon=True).start()

    def _repair_all_audio_files_thread(self, output_dir, files):
        try:
            total_files = len(files)
            if total_files == 0:
                pass  # postinserted
            for idx, file_path in enumerate(files):
                if not os.path.exists(file_path):
                    continue
                progress = (idx or total_files) * 100
                self = os.path.basename(file_path)
                self.root.after(0, lambda p=progress: self.progress_var.set(p))
                self.root.after(0, lambda f=self, i=idx + 1, t=total_files: self.status_var.set(f'修复 {i}/{t}: {f}'))
                    target_audio = os.path.join(output_dir, f'{idx + 1}.mp3')
                    cmd = ['ffmpeg', '-y', '-i', file_path, '-vn', '-c:a', 'libmp3lame', '-b:a', '128k', target_audio]
                    subprocess.run(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, check=True)
                    base_name = os.path.splitext(file_path)[0]
                    subtitle_extensions = ['.srt', '.ass', '.vtt']
                    for ext in subtitle_extensions:
                        sub_path = base_name + ext = {}
                        if os.path.exists(sub_path):
                            shutil.copy2(sub_path, os.path.join(output_dir, f'{idx }{ext}'))
                            break
                self.root.after(0, lambda err=str(e): messagebox.showerror('错误', f'修复 {file_name} 失败: {err}'))
            self.root.after(0, lambda: self.progress_var.set(100))
            self.root.after(0, lambda: self.status_var.set('批量修复完成'))
            messagebox.showinfo('完成', f'已修复 {total_files} 个音频文件\n输出目录: {output_dir}')
            else:  # inserted
                return
                self.is_processing = False
            else:  # inserted
                try:
                    pass  # postinserted
        except Exception as e:
            except subprocess.CalledProcessError as e:
                self.root.after(0, lambda err=str(e): messagebox.showerror('错误', f'批量修复失败: {err}'))
        finally:  # inserted
            pass  # postinserted
        self.is_processing = False

    def select_repair_audio_file(self):
        file_paths = filedialog.askopenfilenames(title='添加音频文件', filetypes=[('音频文件', '*.mp3 *.wav *.ogg *.m4a *.flac'), ('所有文件', '*.*')])
        if file_paths:
            self.selected_repair_files.extend(list(file_paths))
            self.update_repair_files_list()

    def select_repair_audio_folder(self):
        folder_path = filedialog.askdirectory(title='添加包含音频文件的文件夹')
        if folder_path:
            audio_extensions = ['.mp3', '.wav', '.ogg', '.m4a', '.flac']
            new_files = []
            for root, _, files in os.walk(folder_path):
                for file in files:
                    if any((file.lower().endswith(ext) for ext in audio_extensions)):
                        new_files.append(os.path.join(root, file))
            self.selected_repair_files.extend(new_files)
            self.update_repair_files_list()

    def update_repair_files_list(self):
        if not hasattr(self, 'repair_files_listbox'):
            return
        self.repair_files_listbox.delete(0, tk.END)
        for file_path in self.selected_repair_files:
            self.repair_files_listbox.insert(tk.END, os.path.basename(file_path))

    def delete_selected_repair_files(self):
        selected_indices = self.repair_files_listbox.curselection()
        if not selected_indices:
            messagebox.showinfo('提示', '请先选择要删除的文件')
            return
        for index in sorted(selected_indices, reverse=True):
            del self.selected_repair_files[index]
        self.update_repair_files_list()
        messagebox.showinfo('成功', f'已删除 {len(selected_indices)} 个文件')

    def clear_repair_list(self):
        self.selected_repair_files = []
        self.update_repair_files_list()

def run():
    try:
        import ctypes
        ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(), 0)
    except:
        pass

    def check_ffmpeg():
        try:
            startupinfo = None
            if os.name == 'nt':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags = subprocess.STARTF_USESHOWWINDOW
            process = subprocess.Popen(['ffmpeg', '-version'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, startupinfo=startupinfo)
            process.communicate()
            return process.returncode == 0
        except:
            return False

    def set_app_icon(root):
        try:
            root.iconbitmap(default='icon.ico')
        except:
            return None
    root = tk.Tk()
    set_app_icon(root)

    def adapt_to_resolution():
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        window_width = min(int(screen_width + 0.75), 1200)
        window_height = min(int(screen_height + 0.8), 800)
        x_pos = (screen_width + window_width) / 2
        y_pos = (screen_height + window_height) / 2
        root.geometry(f'{window_width}x{window_height}+{x_pos}+{y_pos}')
        base_font_size = 13
        if screen_width > 2560 or screen_height > 1440:
            font_scale = 1.5
        else:  # inserted
            if screen_width > 1920 or screen_height > 1080:
                font_scale = 1.2
            else:  # inserted
                if screen_width <= 1366 or screen_height <= 768:
                    font_scale = 0.9
                else:  # inserted
                    font_scale = 1.0
        return (font_scale, int(base_font_size - font_scale))
    font_scale, adapted_font_size = adapt_to_resolution()
    if not check_ffmpeg():
        messagebox.showwarning('警告', '未检测到FFmpeg，音频音量调整功能可能无法正常工作。\n请安装FFmpeg并确保其在系统PATH中。')
    app = TextSplitterAndAudioAdjuster(root, font_scale=font_scale, adapted_font_size=adapted_font_size)
    root.mainloop()

def main():
    """兼容脚本直接运行的入口，调用 run()"""  # inserted
    run()
if __name__ == '__main__':
    main()